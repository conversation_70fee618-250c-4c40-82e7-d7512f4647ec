<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Push Notification Test - GamyDay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 150px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .info-item h4 {
            color: #495057;
            margin-bottom: 8px;
        }

        .info-item p {
            color: #6c757d;
            font-size: 14px;
            word-break: break-all;
        }

        .log-container {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.info { color: #8be9fd; }
        .log-entry.success { color: #50fa7b; }
        .log-entry.error { color: #ff5555; }
        .log-entry.warning { color: #f1fa8c; }

        .server-config {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .server-config h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }

        .config-label {
            font-weight: 600;
            color: #424242;
        }

        .config-value {
            color: #666;
            font-family: monospace;
        }

        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Push Notification Test</h1>
            <p>Test your GamyDay notification server</p>
        </div>
        
        <div class="content">
            <div class="server-config">
                <h3>🔧 Server Configuration</h3>
                <div class="config-item">
                    <span class="config-label">Server URL:</span>
                    <span class="config-value" id="serverUrl">http://localhost:8000</span>
                </div>
                <div class="config-item">
                    <span class="config-label">VAPID Public Key:</span>
                    <span class="config-value" id="vapidKey">Loading...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Service Worker:</span>
                    <span class="config-value" id="swStatus">Not registered</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Notification Permission:</span>
                    <span class="config-value" id="permissionStatus">Unknown</span>
                </div>
            </div>

            <div id="statusCard" class="status-card">
                <h3>📱 Getting Started</h3>
                <p>Click "Initialize Push Notifications" to begin testing your notification server.</p>
            </div>

            <div class="button-group">
                <button id="initBtn" class="btn btn-primary">🚀 Initialize Push Notifications</button>
                <button id="subscribeBtn" class="btn btn-success" disabled>✅ Subscribe</button>
                <button id="unsubscribeBtn" class="btn btn-danger" disabled>❌ Unsubscribe</button>
                <button id="testBtn" class="btn btn-info" disabled>🧪 Send Test Notification</button>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <h4>🔑 Subscription ID</h4>
                    <p id="subscriptionId">Not subscribed</p>
                </div>
                <div class="info-item">
                    <h4>🌐 Endpoint</h4>
                    <p id="endpoint">Not available</p>
                </div>
                <div class="info-item">
                    <h4>🔐 Auth Key</h4>
                    <p id="authKey">Not available</p>
                </div>
                <div class="info-item">
                    <h4>📊 P256DH Key</h4>
                    <p id="p256dhKey">Not available</p>
                </div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-entry info">📋 Activity Log - Ready to start testing...</div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const SERVER_URL = 'https://gamyday-notification.onrender.com';
        let vapidPublicKey = null;
        let currentSubscription = null;
        let subscriptionId = null;

        // DOM Elements
        const elements = {
            statusCard: document.getElementById('statusCard'),
            initBtn: document.getElementById('initBtn'),
            subscribeBtn: document.getElementById('subscribeBtn'),
            unsubscribeBtn: document.getElementById('unsubscribeBtn'),
            testBtn: document.getElementById('testBtn'),
            serverUrl: document.getElementById('serverUrl'),
            vapidKey: document.getElementById('vapidKey'),
            swStatus: document.getElementById('swStatus'),
            permissionStatus: document.getElementById('permissionStatus'),
            subscriptionId: document.getElementById('subscriptionId'),
            endpoint: document.getElementById('endpoint'),
            authKey: document.getElementById('authKey'),
            p256dhKey: document.getElementById('p256dhKey'),
            logContainer: document.getElementById('logContainer')
        };

        // Initialize
        elements.serverUrl.textContent = SERVER_URL;
        updatePermissionStatus();

        // Utility Functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            elements.logContainer.appendChild(logEntry);
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(message, type = 'info', title = null) {
            elements.statusCard.className = `status-card ${type}`;
            elements.statusCard.innerHTML = `
                <h3>${title || getStatusIcon(type)} ${getStatusTitle(type)}</h3>
                <p>${message}</p>
            `;
        }

        function getStatusIcon(type) {
            const icons = {
                info: '📱',
                success: '✅',
                error: '❌',
                warning: '⚠️'
            };
            return icons[type] || '📱';
        }

        function getStatusTitle(type) {
            const titles = {
                info: 'Information',
                success: 'Success',
                error: 'Error',
                warning: 'Warning'
            };
            return titles[type] || 'Status';
        }

        function updatePermissionStatus() {
            if ('Notification' in window) {
                elements.permissionStatus.textContent = Notification.permission;
            } else {
                elements.permissionStatus.textContent = 'Not supported';
            }
        }

        function setButtonLoading(button, loading) {
            if (loading) {
                button.innerHTML = '<span class="loading"></span>' + button.textContent;
                button.disabled = true;
            } else {
                button.innerHTML = button.textContent.replace(/^.*?([🚀✅❌🧪])/, '$1');
                button.disabled = false;
            }
        }

        // Service Worker Registration
        async function registerServiceWorker() {
            if (!('serviceWorker' in navigator)) {
                throw new Error('Service Worker not supported');
            }

            log('Registering service worker...', 'info');

            // Register external service worker
            const registration = await navigator.serviceWorker.register('./sw.js');
            log('Service worker registered successfully', 'success');
            elements.swStatus.textContent = 'Registered';

            return registration;
        }

        // VAPID Key Functions
        async function fetchVapidPublicKey() {
            try {
                log('Fetching VAPID public key...', 'info');
                const response = await fetch(`${SERVER_URL}/api/vapid-public-key`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                vapidPublicKey = data.public_key;
                elements.vapidKey.textContent = vapidPublicKey.substring(0, 20) + '...';
                log('VAPID public key fetched successfully', 'success');
                return vapidPublicKey;
            } catch (error) {
                log(`Failed to fetch VAPID key: ${error.message}`, 'error');
                elements.vapidKey.textContent = 'Failed to load';
                throw error;
            }
        }

        // Subscription Functions
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }

        async function subscribeToPush() {
            try {
                if (!vapidPublicKey) {
                    throw new Error('VAPID public key not available');
                }

                log('Requesting push subscription...', 'info');
                const registration = await navigator.serviceWorker.ready;

                const subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
                });

                log('Push subscription created', 'success');
                currentSubscription = subscription;
                updateSubscriptionDisplay(subscription);

                // Send subscription to server
                await sendSubscriptionToServer(subscription);

                return subscription;
            } catch (error) {
                log(`Subscription failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function sendSubscriptionToServer(subscription) {
            try {
                log('Sending subscription to server...', 'info');

                const subscriptionData = {
                    subscription: {
                        endpoint: subscription.endpoint,
                        keys: {
                            p256dh: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('p256dh')))),
                            auth: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('auth'))))
                        }
                    },
                    timestamp: new Date().toISOString()
                };

                const response = await fetch(`${SERVER_URL}/api/subscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(subscriptionData)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
                    throw new Error(`HTTP ${response.status}: ${errorData.detail || response.statusText}`);
                }

                const result = await response.json();
                subscriptionId = result.subscription_id;
                elements.subscriptionId.textContent = subscriptionId;

                log(`Subscription sent to server successfully (ID: ${subscriptionId})`, 'success');
                updateStatus('Successfully subscribed to push notifications! You can now receive notifications.', 'success');

                // Update button states
                elements.subscribeBtn.disabled = true;
                elements.unsubscribeBtn.disabled = false;
                elements.testBtn.disabled = false;

                return result;
            } catch (error) {
                log(`Failed to send subscription to server: ${error.message}`, 'error');
                throw error;
            }
        }

        async function unsubscribeFromPush() {
            try {
                if (!currentSubscription) {
                    throw new Error('No active subscription found');
                }

                log('Unsubscribing from push notifications...', 'info');

                // Unsubscribe from browser
                await currentSubscription.unsubscribe();
                log('Browser subscription cancelled', 'success');

                // Notify server
                await sendUnsubscriptionToServer(currentSubscription);

                // Reset state
                currentSubscription = null;
                subscriptionId = null;
                clearSubscriptionDisplay();

                updateStatus('Successfully unsubscribed from push notifications.', 'success');

                // Update button states
                elements.subscribeBtn.disabled = false;
                elements.unsubscribeBtn.disabled = true;
                elements.testBtn.disabled = true;

            } catch (error) {
                log(`Unsubscription failed: ${error.message}`, 'error');
                updateStatus(`Failed to unsubscribe: ${error.message}`, 'error');
            }
        }

        async function sendUnsubscriptionToServer(subscription) {
            try {
                const unsubscribeData = {
                    subscription: {
                        endpoint: subscription.endpoint,
                        keys: {
                            p256dh: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('p256dh')))),
                            auth: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('auth'))))
                        }
                    }
                };

                const response = await fetch(`${SERVER_URL}/api/unsubscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(unsubscribeData)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
                    throw new Error(`HTTP ${response.status}: ${errorData.detail || response.statusText}`);
                }

                const result = await response.json();
                log('Server notified of unsubscription', 'success');
                return result;
            } catch (error) {
                log(`Failed to notify server of unsubscription: ${error.message}`, 'warning');
                // Don't throw here as the browser unsubscription was successful
            }
        }

        function updateSubscriptionDisplay(subscription) {
            if (subscription) {
                elements.endpoint.textContent = subscription.endpoint;
                elements.authKey.textContent = btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('auth'))));
                elements.p256dhKey.textContent = btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('p256dh'))));
            }
        }

        function clearSubscriptionDisplay() {
            elements.subscriptionId.textContent = 'Not subscribed';
            elements.endpoint.textContent = 'Not available';
            elements.authKey.textContent = 'Not available';
            elements.p256dhKey.textContent = 'Not available';
        }

        // Test Notification Function
        async function sendTestNotification() {
            try {
                if (!subscriptionId) {
                    throw new Error('No active subscription found');
                }

                log('Sending test notification...', 'info');

                const testPayload = {
                    title: '🎮 GamyDay Test Notification',
                    body: 'This is a test notification from your GamyDay server! 🚀',
                    icon: '/favicon.png',
                    badge: '/favicon.png',
                    tag: 'test-notification',
                    require_interaction: false,
                    data: {
                        url: window.location.href,
                        timestamp: new Date().toISOString(),
                        test: true
                    },
                    actions: [
                        {
                            action: 'view',
                            title: 'View Details',
                            icon: '/favicon.png'
                        },
                        {
                            action: 'dismiss',
                            title: 'Dismiss'
                        }
                    ]
                };

                const notificationRequest = {
                    payload: testPayload,
                    target_type: 'specific',
                    target_ids: [subscriptionId]
                };

                const response = await fetch(`${SERVER_URL}/api/notifications/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(notificationRequest)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
                    throw new Error(`HTTP ${response.status}: ${errorData.detail || response.statusText}`);
                }

                const result = await response.json();
                log(`Test notification sent successfully! Sent: ${result.result.sent_count}, Failed: ${result.result.failed_count}`, 'success');
                updateStatus('Test notification sent! Check your device for the notification.', 'success');

                return result;
            } catch (error) {
                log(`Failed to send test notification: ${error.message}`, 'error');
                updateStatus(`Failed to send test notification: ${error.message}`, 'error');
                throw error;
            }
        }

        // Permission Functions
        async function requestNotificationPermission() {
            if (!('Notification' in window)) {
                throw new Error('This browser does not support notifications');
            }

            if (Notification.permission === 'granted') {
                log('Notification permission already granted', 'success');
                return true;
            }

            if (Notification.permission === 'denied') {
                throw new Error('Notification permission denied. Please enable notifications in your browser settings.');
            }

            log('Requesting notification permission...', 'info');
            const permission = await Notification.requestPermission();
            updatePermissionStatus();

            if (permission === 'granted') {
                log('Notification permission granted', 'success');
                return true;
            } else {
                throw new Error('Notification permission denied');
            }
        }

        // Main Initialization Function
        async function initializePushNotifications() {
            setButtonLoading(elements.initBtn, true);

            try {
                updateStatus('Initializing push notifications...', 'info');

                // Check browser support
                if (!('serviceWorker' in navigator)) {
                    throw new Error('Service Worker not supported in this browser');
                }

                if (!('PushManager' in window)) {
                    throw new Error('Push messaging not supported in this browser');
                }

                log('Browser supports push notifications', 'success');

                // Request notification permission
                await requestNotificationPermission();

                // Register service worker
                await registerServiceWorker();

                // Fetch VAPID public key
                await fetchVapidPublicKey();

                updateStatus('Push notifications initialized successfully! You can now subscribe to receive notifications.', 'success');

                // Enable subscribe button
                elements.subscribeBtn.disabled = false;
                elements.initBtn.disabled = true;

            } catch (error) {
                log(`Initialization failed: ${error.message}`, 'error');
                updateStatus(`Initialization failed: ${error.message}`, 'error');
            } finally {
                setButtonLoading(elements.initBtn, false);
            }
        }

        // Event Handlers
        elements.initBtn.addEventListener('click', initializePushNotifications);

        elements.subscribeBtn.addEventListener('click', async () => {
            setButtonLoading(elements.subscribeBtn, true);
            try {
                await subscribeToPush();
            } catch (error) {
                updateStatus(`Subscription failed: ${error.message}`, 'error');
            } finally {
                setButtonLoading(elements.subscribeBtn, false);
            }
        });

        elements.unsubscribeBtn.addEventListener('click', async () => {
            setButtonLoading(elements.unsubscribeBtn, true);
            try {
                await unsubscribeFromPush();
            } catch (error) {
                // Error handling is done in the function
            } finally {
                setButtonLoading(elements.unsubscribeBtn, false);
            }
        });

        elements.testBtn.addEventListener('click', async () => {
            setButtonLoading(elements.testBtn, true);
            try {
                await sendTestNotification();
            } catch (error) {
                // Error handling is done in the function
            } finally {
                setButtonLoading(elements.testBtn, false);
            }
        });

        // Check for existing subscription on page load
        window.addEventListener('load', async () => {
            if ('serviceWorker' in navigator && 'PushManager' in window) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        elements.swStatus.textContent = 'Registered';
                        const subscription = await registration.pushManager.getSubscription();
                        if (subscription) {
                            log('Found existing subscription', 'info');
                            currentSubscription = subscription;
                            updateSubscriptionDisplay(subscription);

                            // Try to fetch VAPID key
                            try {
                                await fetchVapidPublicKey();
                                elements.subscribeBtn.disabled = true;
                                elements.unsubscribeBtn.disabled = false;
                                elements.testBtn.disabled = false;
                                updateStatus('Found existing subscription. You can send test notifications or unsubscribe.', 'success');
                            } catch (error) {
                                log('Could not fetch VAPID key for existing subscription', 'warning');
                            }
                        }
                    }
                } catch (error) {
                    log(`Error checking existing subscription: ${error.message}`, 'warning');
                }
            }
        });

        // Add some helpful information
        log('🔧 Browser Information:', 'info');
        log(`User Agent: ${navigator.userAgent}`, 'info');
        log(`Service Worker Support: ${'serviceWorker' in navigator}`, 'info');
        log(`Push Manager Support: ${'PushManager' in window}`, 'info');
        log(`Notification Support: ${'Notification' in window}`, 'info');
        log(`Current Permission: ${Notification.permission}`, 'info');
    </script>
</body>
</html>
