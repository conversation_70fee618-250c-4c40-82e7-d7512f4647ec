"""Main FastAPI application for the notification server."""

import logging
from datetime import datetime
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import time

from .config import settings
from .database import db
from .push_service import push_service
from .admin import admin_router
from .models import (
    SubscriptionCreate,
    NotificationRequest,
    AdminLoginRequest,
    StatsResponse,
    PushSubscription
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting notification server...")
    await db.connect()

    # Fix any subscriptions missing is_active field
    fixed_count = await db.fix_missing_is_active_field()
    if fixed_count > 0:
        logger.info(f"🔧 Database migration: Fixed {fixed_count} subscriptions")

    # Validate VAPID keys
    if not push_service.validate_vapid_keys():
        logger.warning("VAPID keys not properly configured!")

    yield

    # Shutdown
    logger.info("Shutting down notification server...")
    await db.disconnect()


# Create FastAPI app
app = FastAPI(
    title="GamyDay Notification Server",
    description="Web push notification server for GamyDay",
    version="1.0.0",
    lifespan=lifespan
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    client_ip = request.client.host
    method = request.method
    url = str(request.url)
    user_agent = request.headers.get("user-agent", "Unknown")

    logger.info(f"🌐 {method} {url}")
    logger.info(f"   Client: {client_ip}")
    logger.info(f"   User-Agent: {user_agent}")

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(f"   Response: {response.status_code} ({process_time:.3f}s)")

    return response

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include admin router
app.include_router(admin_router)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }


# Subscription endpoints
@app.post("/api/subscribe")
async def subscribe(subscription_data: SubscriptionCreate, request: Request):
    """Subscribe to push notifications."""
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")

    logger.info(f"🔔 SUBSCRIPTION REQUEST RECEIVED")
    logger.info(f"   Client IP: {client_ip}")
    logger.info(f"   User Agent: {user_agent}")
    logger.info(f"   Request Headers: {dict(request.headers)}")

    try:
        # Log the subscription data (without sensitive keys)
        subscription_dict = subscription_data.dict()
        endpoint = subscription_dict.get("subscription", {}).get("endpoint", "Unknown")
        logger.info(f"   Subscription Endpoint: {endpoint[:50]}...")

        # Add request metadata and required fields
        subscription_dict["user_agent"] = user_agent
        subscription_dict["ip_address"] = client_ip
        subscription_dict["is_active"] = True  # 🔧 FIX: Ensure subscription is marked as active
        subscription_dict["notification_count"] = 0
        subscription_dict["last_notification"] = None

        logger.info(f"📝 Creating subscription in database...")
        logger.info(f"   Setting is_active: {subscription_dict['is_active']}")

        # Create subscription in database
        subscription_id = await db.create_subscription(subscription_dict)

        if subscription_id:
            logger.info(f"✅ Subscription created successfully with ID: {subscription_id}")
            return {
                "success": True,
                "message": "Subscription created successfully",
                "subscription_id": subscription_id
            }
        else:
            logger.error(f"❌ Failed to create subscription - database returned None")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create subscription"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"❌ SUBSCRIPTION ERROR: {type(e).__name__}: {e}")
        logger.error(f"   Full traceback:", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/api/unsubscribe")
async def unsubscribe(data: Dict[str, Any]):
    """Unsubscribe from push notifications."""
    try:
        subscription = data.get("subscription")
        if not subscription or not subscription.get("endpoint"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid subscription data"
            )
        
        # Deactivate subscription
        success = await db.deactivate_subscription(subscription["endpoint"])
        
        if success:
            return {
                "success": True,
                "message": "Subscription removed successfully"
            }
        else:
            return {
                "success": False,
                "message": "Subscription not found or already inactive"
            }
    
    except Exception as e:
        logger.error(f"Unsubscribe error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Notification endpoints
@app.post("/api/notifications/send")
async def send_notification(notification_request: NotificationRequest):
    """Send push notification."""
    try:
        # Validate VAPID keys
        if not push_service.validate_vapid_keys():
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="VAPID keys not configured"
            )
        
        # Create notification record
        notification_data = notification_request.dict()
        notification_data["created_at"] = datetime.utcnow()
        notification_data["status"] = "pending"
        notification_data["sent_count"] = 0
        notification_data["failed_count"] = 0
        
        notification_id = await db.create_notification(notification_data)
        
        if not notification_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create notification record"
            )
        
        # Send notification based on target type
        if notification_request.target_type == "all":
            result = await push_service.send_notification_to_all(notification_request.payload)
        elif notification_request.target_type == "specific" and notification_request.target_ids:
            result = await push_service.send_notification_to_specific(
                notification_request.target_ids, 
                notification_request.payload
            )
        elif notification_request.target_type == "recent":
            result = await push_service.send_notification_to_recent(notification_request.payload)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid target type or missing target IDs"
            )
        
        # Update notification status
        status_value = "sent" if result["sent_count"] > 0 else "failed"
        error_message = None if result["sent_count"] > 0 else "No active subscriptions found"
        
        await db.update_notification_status(
            notification_id,
            status_value,
            result["sent_count"],
            result["failed_count"],
            error_message
        )
        
        return {
            "success": True,
            "message": "Notification sent",
            "notification_id": notification_id,
            "result": result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Send notification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Statistics endpoint
@app.get("/api/stats", response_model=StatsResponse)
async def get_stats():
    """Get server statistics."""
    try:
        stats = await db.get_stats()
        return StatsResponse(**stats)
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get statistics"
        )


# VAPID public key endpoint
@app.get("/api/vapid-public-key")
async def get_vapid_public_key(request: Request):
    """Get VAPID public key for client-side subscription."""
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")

    logger.info(f"🔑 VAPID PUBLIC KEY REQUEST")
    logger.info(f"   Client IP: {client_ip}")
    logger.info(f"   User Agent: {user_agent}")
    logger.info(f"   VAPID Public Key: {settings.vapid_public_key[:20]}... (length: {len(settings.vapid_public_key)})")

    if not settings.vapid_public_key:
        logger.error(f"❌ VAPID public key is not configured!")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="VAPID public key not configured"
        )

    return {
        "public_key": settings.vapid_public_key
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
