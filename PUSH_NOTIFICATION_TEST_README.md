# Push Notification Test Suite

This test suite provides comprehensive testing for your GamyDay FastAPI push notification server.

## Files Included

1. **`push-notification-test.html`** - Main test interface with full functionality
2. **`sw.js`** - Optional standalone service worker file
3. **`PUSH_NOTIFICATION_TEST_README.md`** - This documentation

## Features

### 🔧 Server Integration
- Automatically connects to your FastAPI server at `http://localhost:8000`
- Fetches VAPID public key from `/api/vapid-public-key`
- Subscribes/unsubscribes using `/api/subscribe` and `/api/unsubscribe`
- Sends test notifications via `/api/notifications/send`

### 📱 Cross-Platform Support
- Works on desktop browsers (Chrome, Firefox, Edge, Safari)
- Mobile browser support (Android Chrome, iOS Safari with limitations)
- Responsive design for all screen sizes
- Touch-friendly interface

### 🔔 Notification Features
- Service worker registration (inline or external)
- Push subscription management
- Permission handling
- Test notification sending
- Notification click handling
- Background notification processing

### 🛠️ Developer Tools
- Real-time activity logging
- Subscription details display
- Error handling and reporting
- Browser compatibility checking
- VAPID key validation

## Quick Start

### 1. Start Your Server
Make sure your FastAPI server is running:
```bash
cd gamyday-notification-main
python -m uvicorn app.main:app --reload --port 8000
```

### 2. Open the Test File
Simply open `push-notification-test.html` in your browser:
- **Local file**: `file:///path/to/push-notification-test.html`
- **HTTP server**: Serve via any web server for better compatibility

### 3. Test Flow
1. Click **"🚀 Initialize Push Notifications"**
   - Requests notification permissions
   - Registers service worker
   - Fetches VAPID public key

2. Click **"✅ Subscribe"**
   - Creates push subscription
   - Sends subscription to your server
   - Displays subscription details

3. Click **"🧪 Send Test Notification"**
   - Sends a test notification through your server
   - Notification should appear on your device

4. Click **"❌ Unsubscribe"** when done
   - Removes subscription from browser
   - Notifies server to deactivate subscription

## Browser Compatibility

### ✅ Fully Supported
- **Chrome/Chromium** (Desktop & Android)
- **Firefox** (Desktop & Android)
- **Edge** (Desktop)
- **Opera** (Desktop & Android)

### ⚠️ Limited Support
- **Safari** (Desktop: macOS 13+, Mobile: iOS 16.4+)
- **Samsung Internet** (Android)

### ❌ Not Supported
- **Internet Explorer**
- **Older Safari versions**

## Configuration

### Server URL
The test file defaults to `http://localhost:8000`. To change this:

1. Edit the `SERVER_URL` constant in the HTML file:
```javascript
const SERVER_URL = 'https://your-server.com';
```

### Service Worker
The test uses an inline service worker by default. To use the external `sw.js`:

1. Uncomment and modify the service worker registration:
```javascript
const registration = await navigator.serviceWorker.register('/sw.js');
```

## Troubleshooting

### Common Issues

#### 1. "Service Worker not supported"
- **Cause**: Old browser or HTTP (not HTTPS) context
- **Solution**: Use modern browser or serve over HTTPS

#### 2. "VAPID public key not configured"
- **Cause**: Server VAPID keys not set up
- **Solution**: Run `python scripts/generate_vapid_keys.py` and update `.env`

#### 3. "Notification permission denied"
- **Cause**: User denied permission or browser settings
- **Solution**: Reset permissions in browser settings

#### 4. "Failed to fetch VAPID key"
- **Cause**: Server not running or CORS issues
- **Solution**: Check server status and CORS configuration

#### 5. Notifications not appearing
- **Cause**: Browser notification settings or focus state
- **Solution**: Check browser notification settings and test with browser in background

### Mobile Testing

#### Android
- Use Chrome or Firefox
- Ensure notifications are enabled in browser settings
- Test with screen off for background notifications

#### iOS
- Requires iOS 16.4+ and Safari
- Add to home screen for better support
- Limited background notification support

## Server Requirements

Your FastAPI server must have:

1. **VAPID Keys**: Generated and configured in environment
2. **CORS**: Properly configured for your test domain
3. **Endpoints**: All required API endpoints implemented
4. **Database**: MongoDB connection for subscription storage

## Security Notes

- Always use HTTPS in production
- Keep VAPID private key secure
- Validate all client input on server
- Implement rate limiting for notification endpoints
- Consider user privacy and notification frequency

## Advanced Usage

### Custom Notifications
Modify the test notification payload in `sendTestNotification()`:

```javascript
const testPayload = {
    title: 'Custom Title',
    body: 'Custom message',
    icon: '/custom-icon.png',
    data: { customData: 'value' },
    actions: [
        { action: 'action1', title: 'Action 1' },
        { action: 'action2', title: 'Action 2' }
    ]
};
```

### Multiple Subscriptions
The test supports testing multiple subscriptions by opening multiple browser tabs or using different browsers.

### Production Testing
For production testing:
1. Deploy your server with HTTPS
2. Update `SERVER_URL` in the test file
3. Serve test files over HTTPS
4. Test across different devices and networks

## Support

If you encounter issues:
1. Check the browser console for errors
2. Review the activity log in the test interface
3. Verify server logs for API call details
4. Test with different browsers/devices
5. Ensure all dependencies are properly installed

Happy testing! 🚀
