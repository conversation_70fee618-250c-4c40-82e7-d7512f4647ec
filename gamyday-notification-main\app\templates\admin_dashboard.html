{% extends "base.html" %}

{% block title %}Dashboard - GamyDay Notification Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Dashboard</h2>
        <div class="text-sm text-gray-500">
            Last updated: <span id="last-updated"></span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Subscriptions</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_subscriptions }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-user-check text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Subscriptions</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.active_subscriptions }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fas fa-bell text-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Notifications</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_notifications }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 rounded-lg">
                    <i class="fas fa-paper-plane text-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Sent Today</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.notifications_sent_today }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/admin/send-notification" class="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-paper-plane mr-2"></i>
                Send Notification
            </a>
            <a href="/admin/subscriptions" class="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-users mr-2"></i>
                View Subscriptions
            </a>
            <a href="/admin/notifications" class="flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-history mr-2"></i>
                View History
            </a>
        </div>
    </div>

    <!-- Recent Notifications -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Notifications</h3>
        </div>
        <div class="p-6">
            {% if recent_notifications %}
            <div class="space-y-4">
                {% for notification in recent_notifications %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900">{{ notification.payload.title }}</h4>
                        <p class="text-sm text-gray-600">{{ notification.payload.body }}</p>
                        <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                            <span>
                                <i class="fas fa-calendar mr-1"></i>
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </span>
                            <span>
                                <i class="fas fa-users mr-1"></i>
                                {{ notification.sent_count }} sent
                            </span>
                            {% if notification.failed_count > 0 %}
                            <span class="text-red-500">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                {{ notification.failed_count }} failed
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="ml-4">
                        {% if notification.status == 'sent' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>
                            Sent
                        </span>
                        {% elif notification.status == 'failed' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times mr-1"></i>
                            Failed
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-clock mr-1"></i>
                            Pending
                        </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
                <i class="fas fa-bell-slash text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No notifications sent yet</p>
                <a href="/admin/send-notification" class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Your First Notification
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    // Update last updated time
    document.getElementById('last-updated').textContent = new Date().toLocaleString();
    
    // Auto-refresh every 30 seconds
    setTimeout(() => {
        window.location.reload();
    }, 30000);
</script>
{% endblock %}
