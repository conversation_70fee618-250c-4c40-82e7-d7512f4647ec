"""Data models for the notification server."""

from datetime import datetime
from typing import Optional, Dict, Any, List, Annotated
from pydantic import BaseModel, Field, BeforeValidator
from bson import ObjectId


def validate_object_id(v):
    """Validate ObjectId."""
    if isinstance(v, ObjectId):
        return v
    if isinstance(v, str) and ObjectId.is_valid(v):
        return ObjectId(v)
    raise ValueError("Invalid ObjectId")


# Use Annotated for ObjectId validation
PyObjectId = Annotated[ObjectId, BeforeValidator(validate_object_id)]


class PushSubscriptionKeys(BaseModel):
    """Push subscription keys."""
    p256dh: str
    auth: str


class PushSubscription(BaseModel):
    """Push subscription data."""
    endpoint: str
    keys: PushSubscriptionKeys


class SubscriptionCreate(BaseModel):
    """Model for creating a new subscription."""
    subscription: PushSubscription
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None


class SubscriptionInDB(BaseModel):
    """Subscription model as stored in database."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    subscription: PushSubscription
    timestamp: datetime
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    is_active: bool = True
    last_notification: Optional[datetime] = None
    notification_count: int = 0

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class NotificationAction(BaseModel):
    """Notification action button."""
    action: str
    title: str
    icon: Optional[str] = None


class NotificationPayload(BaseModel):
    """Notification payload."""
    title: str
    body: str
    icon: Optional[str] = "/favicon.png"
    badge: Optional[str] = "/favicon.png"
    image: Optional[str] = None
    data: Optional[Dict[str, Any]] = {}
    actions: Optional[List[NotificationAction]] = []
    tag: Optional[str] = None
    require_interaction: Optional[bool] = False
    url: Optional[str] = "/"


class NotificationRequest(BaseModel):
    """Request model for sending notifications."""
    payload: NotificationPayload
    target_type: str = "all"  # "all", "specific", "recent"
    target_ids: Optional[List[str]] = None
    schedule_time: Optional[datetime] = None


class NotificationInDB(BaseModel):
    """Notification model as stored in database."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    payload: NotificationPayload
    target_type: str
    target_ids: Optional[List[str]] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    sent_at: Optional[datetime] = None
    status: str = "pending"  # "pending", "sent", "failed"
    sent_count: int = 0
    failed_count: int = 0
    error_message: Optional[str] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class AdminLoginRequest(BaseModel):
    """Admin login request."""
    username: str
    password: str


class StatsResponse(BaseModel):
    """Statistics response."""
    total_subscriptions: int
    active_subscriptions: int
    total_notifications: int
    notifications_sent_today: int
    recent_notifications: List[Dict[str, Any]]
