// Service Worker for GamyDay Push Notifications
// This file handles push events and notification interactions

console.log('GamyDay Service Worker loaded (external file)');

// Handle push events
self.addEventListener('push', function(event) {
    console.log('Push event received:', event);
    
    let notificationData = {
        title: 'GamyDay Notification',
        body: 'You have a new notification!',
        icon: '/favicon.png',
        badge: '/favicon.png',
        tag: 'gamyday-notification',
        requireInteraction: false,
        data: {
            url: '/',
            timestamp: new Date().toISOString()
        },
        actions: []
    };

    // Parse push data if available
    if (event.data) {
        try {
            const payload = event.data.json();
            console.log('Push payload:', payload);
            notificationData = { ...notificationData, ...payload };
        } catch (e) {
            console.error('Error parsing push data:', e);
            // Fallback to text content
            notificationData.body = event.data.text() || notificationData.body;
        }
    }

    // Show notification
    const notificationPromise = self.registration.showNotification(notificationData.title, {
        body: notificationData.body,
        icon: notificationData.icon,
        badge: notificationData.badge,
        tag: notificationData.tag,
        requireInteraction: notificationData.requireInteraction,
        data: notificationData.data,
        actions: notificationData.actions || [],
        vibrate: [200, 100, 200], // Vibration pattern for mobile
        timestamp: Date.now()
    });

    event.waitUntil(notificationPromise);
});

// Handle notification clicks
self.addEventListener('notificationclick', function(event) {
    console.log('Notification clicked:', event);
    
    // Close the notification
    event.notification.close();

    // Handle action clicks
    if (event.action) {
        console.log('Action clicked:', event.action);
        
        // You can handle different actions here
        switch (event.action) {
            case 'view':
                console.log('View action clicked');
                break;
            case 'dismiss':
                console.log('Dismiss action clicked');
                return; // Don't open a window for dismiss
            default:
                console.log('Unknown action:', event.action);
        }
    }

    // Determine URL to open
    const urlToOpen = event.notification.data?.url || '/';
    console.log('Opening URL:', urlToOpen);
    
    // Focus existing window or open new one
    const clientPromise = clients.matchAll({
        type: 'window',
        includeUncontrolled: true
    }).then(function(clientList) {
        // Try to find an existing window to focus
        for (let i = 0; i < clientList.length; i++) {
            const client = clientList[i];
            if (client.url.includes(urlToOpen) && 'focus' in client) {
                console.log('Focusing existing window');
                return client.focus();
            }
        }
        
        // Open new window if no existing window found
        if (clients.openWindow) {
            console.log('Opening new window');
            return clients.openWindow(urlToOpen);
        }
    });

    event.waitUntil(clientPromise);
});

// Handle notification close events
self.addEventListener('notificationclose', function(event) {
    console.log('Notification closed:', event);
    
    // You can track notification close events here
    // For example, send analytics data
});

// Handle service worker installation
self.addEventListener('install', function(event) {
    console.log('Service Worker installing');
    // Skip waiting to activate immediately
    self.skipWaiting();
});

// Handle service worker activation
self.addEventListener('activate', function(event) {
    console.log('Service Worker activating');
    // Claim all clients immediately
    event.waitUntil(self.clients.claim());
});

// Handle background sync (optional - for offline functionality)
self.addEventListener('sync', function(event) {
    console.log('Background sync event:', event.tag);
    
    if (event.tag === 'background-sync') {
        // Handle background sync tasks here
        console.log('Performing background sync');
    }
});

// Handle message events from the main thread
self.addEventListener('message', function(event) {
    console.log('Service Worker received message:', event.data);
    
    // You can handle messages from the main thread here
    if (event.data && event.data.type) {
        switch (event.data.type) {
            case 'SKIP_WAITING':
                self.skipWaiting();
                break;
            case 'GET_VERSION':
                event.ports[0].postMessage({ version: '1.0.0' });
                break;
            default:
                console.log('Unknown message type:', event.data.type);
        }
    }
});
